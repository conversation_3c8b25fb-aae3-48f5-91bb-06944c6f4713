'use client'

import { faqs } from '@/helpers/content/faq'
import { cn } from '@/utils/cn'
import { motion } from 'motion/react'
import { Fragment, useRef, useState } from 'react'
import { Title } from '../heading/title'
import { sectionPXClassName } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

const MotionTitle = motion.create(Title)

const QUESTIONS_TO_SHOW_WHEN_CLOSED = 5

export function FAQ() {
  const faqRef = useRef<HTMLDivElement>(null)
  const [openQuestions, setOpenQuestions] = useState<string[]>([])
  const [showEntireFAQ, setShowEntireFAQ] = useState(false)

  const faqsToShow = showEntireFAQ ? faqs : [faqs[0]]

  return (
    <>
      <section className={cn('bg-[#181818] pb-0 border-t border-[#A3A3A3]', 'pb-0')} id='faqs'>
        <SectionContent>
          <MotionTitle
            initial={{ opacity: 0, height: 0 }}
            whileInView={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className={cn(sectionPXClassName, 'py-[50px] lte-1260:py-[40px]')}
          >
            FAQS
          </MotionTitle>
          <div ref={faqRef} className={cn('text-[#EEF2F7] duration-1000 overflow-hidden')}>
            {faqsToShow.map((item) => (
              <Fragment key={item.title}>
                <h2 className={cn(sectionPXClassName, 'bg-[#404040] py-[30px] uppercase text-xl font-bold text-left')}>
                  {item.title}
                </h2>
                <ul>
                  {item.questionsAndAnswers
                    .slice(0, showEntireFAQ ? item.questionsAndAnswers.length : QUESTIONS_TO_SHOW_WHEN_CLOSED)
                    .map((qa) => (
                      <motion.li
                        key={qa.question}
                        initial={{ opacity: 0, x: -30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.7 }}
                        viewport={{ once: true }}
                        className={cn(sectionPXClassName, 'py-[30px] border-b border-[#404040]')}
                      >
                        <button
                          className='flex justify-between w-full text-left'
                          onClick={() =>
                            setOpenQuestions((p) =>
                              p.includes(qa.question) ? p.filter((x) => x !== qa.question) : [...p, qa.question],
                            )
                          }
                        >
                          <p className='uppercase text-xl font-bold'>{qa.question}</p>
                          <img
                            src='./svgs/gradient-arrow.svg'
                            className={cn(
                              'duration-500',
                              openQuestions.includes(qa.question) ? 'rotate-90' : 'rotate-0',
                            )}
                          />
                        </button>
                        <div
                          className={cn(
                            'pt-[15px] overflow-hidden duration-500 text-white text-sm font-[450] space-y-5',
                            openQuestions.includes(qa.question) ? 'h-auto max-h-max' : 'max-h h-0 pt-0',
                          )}
                        >
                          {qa.answer}
                        </div>
                      </motion.li>
                    ))}
                </ul>
              </Fragment>
            ))}
          </div>
        </SectionContent>
      </section>
      <Button
        hideShadow
        variant='gradient'
        onClick={() => setShowEntireFAQ((p) => !p)}
        className='text-center w-full flex items-center justify-center h-20 text-lg'
      >
        View {showEntireFAQ ? 'less' : 'more'} FAQ{' '}
        <img
          src='./svgs/icons/chevron-down-faq.svg'
          alt='chevron down'
          className={cn('duration-500', showEntireFAQ ? 'rotate-180' : 'rotate-0')}
        />
      </Button>
    </>
  )
}
