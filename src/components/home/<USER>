'use client'

import { howItWorks } from '@/helpers/content/how-it-works'
import { useResponsive } from '@/hooks/useResponsive'
import { cn } from '@/utils/cn'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Section, sectionPXClassName } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

export function HowItWorks() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <Section className={cn('bg-[#181818] lte-1260:p-0 lte-1024:p-0 lte-1024:pb-[100px]')} id='how-it-works'>
      <SectionContent className={cn('space-y-[50px] relative')}>
        {!isScreenSmallerOrEqual('1260') && (
          <>
            <motion.img
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              src='./svgs/corner-gradient.svg'
              className='absolute right-0 top-0'
            />
            <motion.img
              initial={{ opacity: 0, scale: 0, rotate: 180 }}
              whileInView={{ opacity: 1, scale: 1, rotate: 180 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              src='./svgs/corner-gradient.svg'
              className='absolute left-0 bottom-0'
            />
          </>
        )}

        <div className={cn('grid grid-cols-2 lte-1260:grid-cols-1 items-center gap-[30px]', 'pt-[20px] lte-1260:pt-0')}>
          <motion.img
            initial={{ opacity: 0, x: -100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            src='./images/general/how-laptop-and-hands.jpg'
            alt='Man using laptop and mouse'
            className='w-full'
          />

          <motion.div
            initial={{ opacity: 0, x: 100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className={cn('space-y-[50px]', sectionPXClassName)}
          >
            <Heading>
              <DashTitle className='text-white'>subscription</DashTitle>
              <Title className='uppercase'>How It Works</Title>
            </Heading>

            <ul className='text-white space-y-[31px]'>
              {howItWorks.map((item, index) => (
                <li
                  key={`how-it-works_${index}_${item.title}`}
                  className='grid grid-cols-[max-content,auto] items-center gap-2'
                >
                  <span className='font-bold text-[60px] leading-[60px] text-center w-[90px]'>{index + 1}.</span>
                  <div className='text-sm space-y-3'>
                    <h3 className='font-bold text-base uppercase'>{item.title}</h3>
                    <>{item.description}</>
                  </div>
                </li>
              ))}
            </ul>

            <Button asChild>
              <a href={links.keylightStore} target='_blank'>
                SELECT LAPTOP
              </a>
            </Button>
          </motion.div>
        </div>

        <div
          className={cn(
            'grid grid-cols-2 items-center gap-[30px] lte-1260:grid-cols-1',
            'pb-[20px] lte-1260:pb-[100px]',
          )}
        >
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
            className={cn('space-y-[50px]', sectionPXClassName)}
          >
            <Heading>
              <DashTitle className='text-white'>upgrades</DashTitle>
              <Title>How to upgrade your gaming laptop</Title>
            </Heading>

            <div>
              <p className='text-white text-sm'>
                Never fall behind the gaming curve again. Every 1 year, OMEN Gaming Subscription allows you to trade in
                your current device for the newest model, ensuring your access to cutting-edge processors,
                next-generation graphics cards, and breakthrough display technology. Simply log in to initiate your
                upgrade and choose your new tech. 
              </p>
              <ul className='list-disc space-y-4 mt-4 pl-6 text-white'>
                <li>
                  Stay at the forefront of gaming technology so you can experience the latest AAA titles at their
                  absolute best.
                </li>

                <li>No more saving up for years to upgrade—it's built into your subscription.  </li>

                <li>
                  Expert technical support included throughout your subscription.<sup>3</sup>
                </li>
              </ul>
            </div>

            <Button asChild>
              <a href={links.keylightStore} target='_blank'>
                Select laptop
              </a>
            </Button>
          </motion.div>

          <motion.img
            initial={{ opacity: 0, x: 100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
            src='./images/general/how-dual-laptops.jpg'
            alt='Man using laptop and mouse'
            className='w-full lte-1260:order-first'
          />
        </div>
      </SectionContent>
    </Section>
  )
}
