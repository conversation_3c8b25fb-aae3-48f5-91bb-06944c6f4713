'use client'

import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { columns, featuresTable } from '@/helpers/content/features-table'
import { useResponsive } from '@/hooks/useResponsive'
import { cn, dualGradient } from '@/utils/cn'
import { motion } from 'motion/react'
import { Fragment } from 'react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Modal } from '../modal'
import { Section } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'

const MotionTable = motion.create(Table)

export function FeaturesTable() {
  const { isScreenSmallerOrEqual } = useResponsive()

  const subscribeColumn = columns.find((column) => column.title === 'subscribe')!

  return (
    <Section className='bg-white'>
      <SectionContent className='space-y-[50px]'>
        <Heading>
          <DashTitle>COMPARE FEATURES</DashTitle>
          <Title>Is the OMEN Gaming Subscription right for me?</Title>
        </Heading>

        <MotionTable
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
        >
          <TableHeader>
            <TableRow className='border-0'>
              {!isScreenSmallerOrEqual('1024') && <TableHead className='w-1/4 border-0'></TableHead>}
              {columns.map((column, index) => (
                <TableHead
                  key={index}
                  className={cn(
                    'w-1/4 lte-1024:w-1/3 h-[72px] lte-1024:h-[98px] lte-375:h-[121px] border border-neutral-400 relative',
                    column.highlight && 'bg-[#EEF2F7] lte-1024:bg-[#D9D9D9]',
                  )}
                >
                  {column.highlight && (
                    <Badge
                      className={cn(
                        'uppercase absolute top-0 right-0 bg-gradient-to-b text-white font-bold px-2 py-1 rounded-b-[8px]',
                        dualGradient,
                      )}
                    >
                      new
                    </Badge>
                  )}
                  <div className='flex flex-col items-center gap-2 text-[#404040] text-center'>
                    <span className='text-base font-bold uppercase'>{column.title}</span>
                    <span className='text-sm font-[325]'>{column.subtitle}</span>
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>

          <TableBody>
            <>
              {featuresTable.map((row, rowIndex) => (
                <Fragment key={rowIndex}>
                  {isScreenSmallerOrEqual('1024') && (
                    <TableRow>
                      <TableCell className='font-bold max-w-[336px] uppercase p-0 pt-[22px] pb-[7px]' colSpan={2}>
                        {row.title}
                      </TableCell>
                      <TableCell className='p-0 pt-[22px] pb-[7px] flex justify-end'>
                        <Modal
                          trigger={
                            <button>
                              <img src='./svgs/icons/info.svg' />
                            </button>
                          }
                        >
                          <div className='space-y-[20px]'>
                            <h3 className='font-bold text-lg'>{row.title}</h3>
                            <p className='text-base font-[450] text-[#666666]'>{row.info}</p>
                          </div>
                        </Modal>
                      </TableCell>
                    </TableRow>
                  )}

                  <TableRow>
                    {!isScreenSmallerOrEqual('1024') && (
                      <TableCell
                        className={cn(
                          'grid grid-cols-[auto_max-content] items-center h-[72px] border-neutral-400 border-l gap-4',
                          rowIndex === 0 ? 'border-t border-b' : 'border-b',
                        )}
                      >
                        <div className='font-bold max-w-[336px] uppercase'>{row.title}</div>
                        <Modal
                          trigger={
                            <button>
                              <img src='./svgs/icons/info.svg' />
                            </button>
                          }
                        >
                          <div className='space-y-[20px]'>
                            <h3 className='font-bold text-lg'>{row.title}</h3>
                            <p className='text-base font-[450] text-[#666666]'>{row.info}</p>
                          </div>
                        </Modal>
                      </TableCell>
                    )}

                    {columns.map((column, colIndex) => {
                      return (
                        <TableCell
                          key={colIndex}
                          className={cn(
                            'h-[72px] border border-neutral-400  text-center',
                            column.highlight && 'bg-[#EEF2F7] lte-1024:bg-[#D9D9D9]',
                          )}
                        >
                          <div className='flex justify-center items-center'>{row.columns[column.title]}</div>
                        </TableCell>
                      )
                    })}
                  </TableRow>
                </Fragment>
              ))}
            </>

            {!isScreenSmallerOrEqual('1024') && (
              <TableRow>
                <TableCell className='border-none border-neutral-400'></TableCell>
                {columns.map((column) => (
                  <TableCell key={column.title} className='h-[72px] border border-neutral-400'>
                    <Button
                      variant={column.highlight ? 'gradient' : 'black'}
                      className='w-full [&_svg]:size-5 [&_svg]:shrink-0'
                      hideShadow
                      asChild
                    >
                      <a href={column.cta.href} target='_blank'>
                        {column.cta.text}
                        {!column.highlight && <img src='./svgs/icons/chevron-right.svg' />}
                      </a>
                    </Button>
                  </TableCell>
                ))}
              </TableRow>
            )}
          </TableBody>
        </MotionTable>

        <div className='flex justify-center'>
          <Button variant={subscribeColumn.highlight ? 'gradient' : 'black'} asChild>
            <a href={subscribeColumn.cta.href} target='_blank'>
              {subscribeColumn.cta.text}
              {!subscribeColumn.highlight && <img src='./svgs/icons/chevron-right.svg' />}
            </a>
          </Button>
        </div>
      </SectionContent>
    </Section>
  )
}
