'use client'

import { benefits } from '@/helpers/content/benefits'
import { links } from '@/utils/links'
import { motion } from 'motion/react'
import { Heading } from '../heading'
import { DashTitle } from '../heading/dash-title'
import { Title } from '../heading/title'
import { Section } from '../section'
import { SectionContent } from '../section/section-content'
import { Button } from '../ui/button'
import { useResponsive } from '@/hooks/useResponsive'

const MotionButton = motion.create(Button)

export function Benefits() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <>
      <motion.img
        initial={{ opacity: 0, filter: 'blur(10px)' }}
        whileInView={{ opacity: 1, filter: 'blur(0px)' }}
        transition={{ duration: 0.7 }}
        viewport={{ once: true }}
        src={
          isScreenSmallerOrEqual('1024')
            ? './images/general/man-in-desktop-mobile.jpg'
            : './images/general/man-in-desktop.jpg'
        }
        className='w-full'
      />
      <Section className='py-[150px] bg-[#181818]'>
        <SectionContent className='space-y-[50px]'>
          <Heading>
            <DashTitle className='text-white'>Benefits</DashTitle>
            <Title>The OMEN Gaming Subscription difference</Title>
          </Heading>
          <div className='grid grid-cols-3 lte-1024:grid-cols-1 gap-8'>
            {benefits.map((benefit, index) => (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 * index }}
                viewport={{ once: true }}
                key={benefit.title}
                className='flex gap-5 items-start'
              >
                <img src={benefit.icon} className='w-10' />
                <div className='space-y-2.5'>
                  <h2 className='text-base font-bold uppercase text-white'>{benefit.title}</h2>
                  <div className='text-white text-base font-[450]'>{benefit.description}</div>
                </div>
              </motion.div>
            ))}
          </div>
          <MotionButton
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
            asChild
          >
            <a href={links.keylightStore} target='_blank'>
              Select laptop
            </a>
          </MotionButton>
        </SectionContent>
      </Section>
      <motion.img
        initial={{ opacity: 0, filter: 'blur(10px)' }}
        whileInView={{ opacity: 1, filter: 'blur(0px)' }}
        transition={{ duration: 0.7 }}
        viewport={{ once: true }}
        src={'./images/general/trial.jpg'}
        className='w-full h-full'
      />
    </>
  )
}
