'use client'

import { features } from '@/helpers/content/subscription-includes'
import { useResponsive } from '@/hooks/useResponsive'
import { cn, dualGradient } from '@/utils/cn'
import { motion } from 'motion/react'

import { SectionContent } from '../section/section-content'

export function SubscriptionIncludes() {
  const { isScreenSmallerOrEqual } = useResponsive()

  return (
    <section className={cn(dualGradient, 'py-[50px]')}>
      <SectionContent className='space-y-[25px] text-center text-white'>
        <motion.h3
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          className='text-xl font-bold leading-[25px] uppercase'
        >
          Every subscription includes:
        </motion.h3>

        <ul
          className={cn(
            'flex justify-center gap-[92px] text-base w-full',
            isScreenSmallerOrEqual('1260') && 'flex-col items-center gap-[5px]',
          )}
        >
          {features.map((feature, index) => (
            <motion.li
              key={index}
              initial={{ opacity: 0, scale: 0 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.3 * (index + 1) }}
              viewport={{ once: true }}
              className='flex items-center gap-[5px]'
            >
              <img src={feature.icon} />
              <div className='w-fit font-medium text-white text-xl uppercase whitespace-nowrap'>{feature.text}</div>
            </motion.li>
          ))}
        </ul>
      </SectionContent>
    </section>
  )
}
