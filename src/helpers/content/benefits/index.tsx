import { Link } from '@/components/link'
import type { Benefit } from './types'

export const benefits: Benefit[] = [
  {
    icon: './svgs/benefits/laptop-change.svg',
    title: 'Laptop upgrades included',
    description: (
      <>
        <p>
          The traditional upgrade cycle keeps most gamers perpetually one step behind. But with access to a new laptop
          every year, your OMEN Gaming Subscription breaks that cycle completely, ensuring your hardware evolves as
          rapidly as the latest games do. The future of gaming doesn’t wait. Why should you?
        </p>
      </>
    ),
  },
  {
    icon: './svgs/benefits/omen-logo.svg',
    title: 'Your all-in-one gaming solution',
    description: (
      <p>
        Whether it's the performance of the latest OMEN gaming laptop, the immersive possibilities of HyperX
        accessories, or the security of 24/7 customer support,<sup>3</sup> OMEN Gaming Subscription delivers a premium
        gaming experience with unparalleled peace of mind.
      </p>
    ),
  },
  {
    icon: './svgs/benefits/person-shield.svg',
    title: 'Data security and protection',
    description: (
      <>
        <p>
          We take your privacy and data seriously. We do not have access to any of your personal files on the laptop at
          any time during your subscription, and our support team will assist you in removing all data prior to you
          returning the laptop.
        </p>
        <br />
        <Link
          href='https://www.hp.com/us-en/privacy/privacy.html?jumpid=in_R11928_/us/en/corp/privacy-central/privacy-statements'
          className='text-inherit'
        >
          View our HP Privacy Statement for more information.
        </Link>
      </>
    ),
  },
]
